import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';

interface GoogleSignInButtonProps {
  onSuccess: (credential: string) => void;
  onError?: (error: any) => void;
  size?: 'large' | 'medium' | 'small';
  theme?: 'outline' | 'filled_blue' | 'filled_black';
  text?: 'signin_with' | 'signup_with' | 'continue_with' | 'signin';
  shape?: 'rectangular' | 'pill' | 'circle' | 'square';
  width?: number;
}

declare global {
  interface Window {
    google?: {
      accounts: {
        id: {
          initialize: (config: any) => void;
          renderButton: (element: HTMLElement, config: any) => void;
          prompt: () => void;
        };
      };
    };
    CredentialsContainer?: any;
  }
}

const GoogleSignInButton: React.FC<GoogleSignInButtonProps> = ({
  onSuccess,
  onError,
  size = 'large',
  theme = 'outline',
  text = 'signin_with',
  shape = 'rectangular',
  width = 300
}) => {
  const buttonRef = useRef<HTMLDivElement>(null);
  const isInitialized = useRef(false);

  useEffect(() => {
    const initializeGoogleSignIn = () => {
      if (!window.google || isInitialized.current) {
        return;
      }

      try {
        // Get client ID from environment or use the one from backend config
        const clientId = process.env.REACT_APP_GOOGLE_CLIENT_ID || '************-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com';

        // Determine the current origin for FedCM configuration
        const currentOrigin = window.location.origin;
        const isProduction = currentOrigin === 'https://gallerytuner.com';
        const isDevelopment = currentOrigin.includes('localhost');

        console.log('Initializing Google Sign-In with FedCM support...');
        console.log('Current origin:', currentOrigin);
        console.log('Is production:', isProduction);
        console.log('Is development:', isDevelopment);

        // Check if browser supports FedCM
        const supportsFedCM = window.CredentialsContainer && 'get' in window.CredentialsContainer.prototype;
        console.log('Browser supports FedCM:', supportsFedCM);

        window.google.accounts.id.initialize({
          client_id: clientId,
          callback: (response: any) => {
            console.log('Google Sign-In response received:', response);
            if (response.credential) {
              onSuccess(response.credential);
            } else if (onError) {
              onError(new Error('No credential received'));
            }
          },
          auto_select: false,
          cancel_on_tap_outside: true,
          // FedCM configuration
          use_fedcm_for_prompt: true, // Enable FedCM for One Tap prompts
          // Note: use_fedcm_for_button is not needed for button rendering
          // as it's primarily for One Tap prompts
        });

        if (buttonRef.current) {
          window.google.accounts.id.renderButton(buttonRef.current, {
            type: 'standard',
            size: size,
            theme: theme,
            text: text,
            shape: shape,
            width: width,
          });
        }

        isInitialized.current = true;
      } catch (error) {
        console.error('Error initializing Google Sign-In with FedCM:', error);

        // Provide specific guidance for FedCM-related errors
        const errorMessage = (error as Error).message;
        if (errorMessage.includes('NotAllowedError')) {
          console.error('FedCM Error: identity-credentials-get permission not enabled in iframe');
          console.error('Solution: Add allow="identity-credentials-get" to parent iframe tag');
        } else if (errorMessage.includes('NetworkError')) {
          console.error('FedCM Error: Content Security Policy blocking FedCM');
          console.error('Solution: Update CSP to allow https://accounts.google.com');
        } else if (errorMessage.includes('SecurityError')) {
          console.error('FedCM Error: Cross-origin iframe restrictions');
          console.error('Solution: Use same-site cross-origin iframes only');
        }

        if (onError) {
          onError(error);
        }
      }
    };

    // Check if Google Identity Services is already loaded
    if (window.google) {
      initializeGoogleSignIn();
    } else {
      // Wait for the script to load
      const checkGoogleLoaded = setInterval(() => {
        if (window.google) {
          clearInterval(checkGoogleLoaded);
          initializeGoogleSignIn();
        }
      }, 100);

      // Cleanup interval after 10 seconds
      setTimeout(() => {
        clearInterval(checkGoogleLoaded);
      }, 10000);

      return () => {
        clearInterval(checkGoogleLoaded);
      };
    }
  }, [onSuccess, onError, size, theme, text, shape, width]);

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <div ref={buttonRef} />
    </Box>
  );
};

export default GoogleSignInButton;
