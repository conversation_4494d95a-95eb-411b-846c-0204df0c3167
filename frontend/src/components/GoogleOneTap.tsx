import React, { useEffect, useRef } from 'react';

interface GoogleOneTapProps {
  onSuccess: (credential: string) => void;
  onError?: (error: any) => void;
  disabled?: boolean;
}

// Type for Google One Tap API (separate from existing GoogleSignInButton types)
// Updated for FedCM compatibility
interface GoogleOneTapAPI {
  initialize: (config: any) => void;
  prompt: (callback?: (notification: any) => void) => void;
  cancel?: () => void;
  disableAutoSelect?: () => void;
}

// FedCM Migration Notes:
// 1. This component is now FedCM-compliant with use_fedcm_for_prompt: true
// 2. If used in cross-origin iframes, parent frames must include: allow="identity-credentials-get"
// 3. Custom positioning is not supported with FedCM - browser controls prompt position
// 4. Display moment methods (isNotDisplayed, getNotDisplayedReason) are removed for privacy

const GoogleOneTap: React.FC<GoogleOneTapProps> = ({ onSuccess, onError, disabled = false }) => {
  const hasInitialized = useRef(false);
  const isPromptShown = useRef(false);

  useEffect(() => {
    // Don't initialize if disabled or already initialized
    if (disabled || hasInitialized.current) {
      return;
    }

    // Optional: Skip One Tap for debugging purposes
    const skipOneTap = process.env.REACT_APP_SKIP_ONE_TAP === 'true';
    if (skipOneTap) {
      console.log('Google One Tap disabled via REACT_APP_SKIP_ONE_TAP environment variable');
      return;
    }

    const initializeOneTap = () => {
      if (!window.google?.accounts?.id) {
        console.log('Google Identity Services not loaded yet, retrying...');
        setTimeout(initializeOneTap, 100);
        return;
      }

      try {
        // Get client ID from environment or use the one from backend config
        const clientId = process.env.REACT_APP_GOOGLE_CLIENT_ID || '************-oudag9d2btbee2n0m3ulh9c9pa5dr7fq.apps.googleusercontent.com';

        console.log('Initializing Google One Tap with FedCM...');
        console.log('Current origin:', window.location.origin);
        console.log('Client ID:', clientId);
        console.log('FedCM enabled: true');

        // Check if browser supports FedCM
        if (!window.CredentialsContainer || !('get' in window.CredentialsContainer.prototype)) {
          console.warn('FedCM may not be fully supported in this browser');
        }

        // Use type assertion to access One Tap API
        const googleId = window.google.accounts.id as GoogleOneTapAPI;

        googleId.initialize({
          client_id: clientId,
          callback: (response: any) => {
            console.log('Google One Tap response received:', response);
            if (response.credential) {
              onSuccess(response.credential);
            } else if (onError) {
              onError(new Error('No credential received from One Tap'));
            }
          },
          auto_select: true, // Enable auto-select for returning users
          cancel_on_tap_outside: true,
          context: 'signin', // Can be 'signin', 'signup', or 'use'
          ux_mode: 'popup', // Use popup mode for better UX
          itp_support: true, // Enable Intelligent Tracking Prevention support
          use_fedcm_for_prompt: true, // Enable FedCM for One Tap (required for migration)
          use_fedcm_for_button: true, // Enable FedCM for Button flow (optional but recommended)
        });

        hasInitialized.current = true;

        // Add a small delay before showing the prompt to ensure the page is fully loaded
        setTimeout(() => {
          if (!disabled && !isPromptShown.current && window.google?.accounts?.id) {
            // Show the One Tap prompt with type assertion
            const googleId = window.google.accounts.id as GoogleOneTapAPI;

            googleId.prompt((notification: any) => {
              console.log('Google One Tap notification:', notification);

              // FedCM Migration: Removed isNotDisplayed(), isDisplayed(), isDisplayMoment(),
              // and getNotDisplayedReason() methods as they are no longer available with FedCM
              // for improved user privacy.

              // FedCM Migration: isSkippedMoment() is still available but getSkippedReason()
              // is removed for privacy. Only check if moment was skipped without detailed reason.
              if (notification.isSkippedMoment()) {
                console.log('Google One Tap was skipped by user or system');
                // Note: getSkippedReason() is no longer available with FedCM
              } else if (notification.isDismissedMoment()) {
                // getDismissedReason() is still available with FedCM
                const reason = notification.getDismissedReason();
                console.log('Google One Tap dismissed:', reason);
                // Possible reasons:
                // - credential_returned: Successfully returned credential
                // - cancel_called: Cancel was called programmatically
                // - flow_restarted: Flow was restarted
              }

              isPromptShown.current = false;
            });

            isPromptShown.current = true;
            console.log('Google One Tap prompt initiated');
          }
        }, 1000); // 1 second delay

      } catch (error) {
        console.error('Error initializing Google One Tap with FedCM:', error);

        // Provide specific guidance for FedCM-related errors
        const errorMessage = (error as Error).message;
        if (errorMessage.includes('NotAllowedError')) {
          console.error('FedCM Error: identity-credentials-get permission not enabled in iframe');
          console.error('Solution: Add allow="identity-credentials-get" to parent iframe tag');
        } else if (errorMessage.includes('NetworkError')) {
          console.error('FedCM Error: Content Security Policy blocking FedCM');
          console.error('Solution: Update CSP to allow https://accounts.google.com');
        } else if (errorMessage.includes('SecurityError')) {
          console.error('FedCM Error: Cross-origin iframe restrictions');
          console.error('Solution: Use same-site cross-origin iframes only');
        }

        if (onError) {
          onError(error as Error);
        }
      }
    };

    // Start initialization
    initializeOneTap();

    // Cleanup function
    return () => {
      if (window.google?.accounts?.id && isPromptShown.current) {
        try {
          const googleId = window.google.accounts.id as GoogleOneTapAPI;
          if (googleId.cancel) {
            googleId.cancel();
          }
        } catch (error) {
          console.warn('Error canceling Google One Tap:', error);
        }
      }
    };
  }, [disabled, onSuccess, onError]);

  // Cancel One Tap when component becomes disabled
  useEffect(() => {
    if (disabled && window.google?.accounts?.id && isPromptShown.current) {
      try {
        const googleId = window.google.accounts.id as GoogleOneTapAPI;
        if (googleId.cancel) {
          googleId.cancel();
        }
        isPromptShown.current = false;
      } catch (error) {
        console.warn('Error canceling Google One Tap on disable:', error);
      }
    }
  }, [disabled]);

  // This component doesn't render anything visible
  return null;
};

export default GoogleOneTap;
