import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import GoogleSignInButton from '../GoogleSignInButton';

// Mock the Google Identity Services
const mockGoogle = {
  accounts: {
    id: {
      initialize: jest.fn(),
      renderButton: jest.fn(),
      prompt: jest.fn(),
    },
  },
};

// Mock window.google
Object.defineProperty(window, 'google', {
  value: mockGoogle,
  writable: true,
});

// Mock CredentialsContainer for FedCM support detection
Object.defineProperty(window, 'CredentialsContainer', {
  value: {
    prototype: {
      get: jest.fn(),
    },
  },
  writable: true,
});

describe('GoogleSignInButton with FedCM', () => {
  const mockOnSuccess = jest.fn();
  const mockOnError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
    // Reset the google mock
    mockGoogle.accounts.id.initialize.mockClear();
    mockGoogle.accounts.id.renderButton.mockClear();
  });

  it('renders without crashing', () => {
    const { container } = render(
      <GoogleSignInButton
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );

    expect(container.firstChild).toBeInTheDocument();
  });

  it('initializes Google Sign-In with FedCM configuration', async () => {
    render(
      <GoogleSignInButton
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );

    await waitFor(() => {
      expect(mockGoogle.accounts.id.initialize).toHaveBeenCalledWith(
        expect.objectContaining({
          use_fedcm_for_prompt: true,
          auto_select: false,
          cancel_on_tap_outside: true,
        })
      );
    });
  });

  it('renders button with correct configuration', async () => {
    render(
      <GoogleSignInButton
        onSuccess={mockOnSuccess}
        onError={mockOnError}
        size="large"
        theme="outline"
        text="signin_with"
        width={300}
      />
    );

    await waitFor(() => {
      expect(mockGoogle.accounts.id.renderButton).toHaveBeenCalledWith(
        expect.any(HTMLElement),
        expect.objectContaining({
          type: 'standard',
          size: 'large',
          theme: 'outline',
          text: 'signin_with',
          width: 300,
        })
      );
    });
  });

  it('handles successful authentication', async () => {
    const mockCredential = 'mock-jwt-token';
    
    // Mock the callback to simulate successful authentication
    mockGoogle.accounts.id.initialize.mockImplementation((config) => {
      // Simulate successful response
      config.callback({ credential: mockCredential });
    });

    render(
      <GoogleSignInButton
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );

    await waitFor(() => {
      expect(mockOnSuccess).toHaveBeenCalledWith(mockCredential);
    });
  });

  it('handles authentication errors', async () => {
    // Mock the callback to simulate error
    mockGoogle.accounts.id.initialize.mockImplementation((config) => {
      // Simulate error response
      config.callback({});
    });

    render(
      <GoogleSignInButton
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );

    await waitFor(() => {
      expect(mockOnError).toHaveBeenCalledWith(
        expect.objectContaining({
          message: 'No credential received',
        })
      );
    });
  });

  it('detects FedCM support correctly', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    render(
      <GoogleSignInButton
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        'Browser supports FedCM:',
        true
      );
    });

    consoleSpy.mockRestore();
  });

  it('logs environment information', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

    render(
      <GoogleSignInButton
        onSuccess={mockOnSuccess}
        onError={mockOnError}
      />
    );

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        'Initializing Google Sign-In with FedCM support...'
      );
      expect(consoleSpy).toHaveBeenCalledWith(
        'Current origin:',
        'http://localhost'
      );
    });

    consoleSpy.mockRestore();
  });
});
