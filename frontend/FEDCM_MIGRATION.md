# FedCM Migration for Gallery Tuner

This document outlines the migration to Federated Credential Management (FedCM) APIs for Google Sign-In in Gallery Tuner.

## What is FedCM?

FedCM (Federated Credential Management) is a web standard that provides a privacy-preserving approach to federated identity on the web. It's designed to allow users to login to websites with their federated accounts in a way that doesn't allow tracking across sites.

## Changes Made

### 1. Updated GoogleSignInButton Component

- Added FedCM support with `use_fedcm_for_prompt: true`
- Enhanced error handling for FedCM-specific errors
- Added environment detection for dev/prod configurations
- Improved logging for debugging

### 2. Created GoogleOneTapFedCM Component

- New component specifically for FedCM-enabled One Tap experience
- Automatic sign-in for returning users
- Proper error handling and cleanup
- Environment-aware configuration

### 3. HTML Meta Tags

- Added `Permissions-Policy` meta tag for `identity-credentials-get=*`
- This allows the FedCM API to function properly

### 4. Environment Configuration

- Updated `.env` files with FedCM-specific settings
- Added production environment configuration
- Environment detection for localhost vs gallerytuner.com

## Browser Support

FedCM is supported in:
- Chrome 108+
- Edge 108+
- Other Chromium-based browsers

For browsers that don't support FedCM, the implementation gracefully falls back to the standard Google Identity Services flow.

## Testing

### Development Environment
- URL: `http://localhost:3000`
- FedCM will work with proper browser support
- Console logging enabled for debugging

### Production Environment
- URL: `https://gallerytuner.com`
- FedCM fully enabled
- Optimized for production use

## Configuration Options

### Environment Variables

- `REACT_APP_SKIP_ONE_TAP=true` - Disable One Tap for debugging
- `REACT_APP_GOOGLE_CLIENT_ID` - Google OAuth Client ID
- `REACT_APP_ENVIRONMENT` - Override environment detection

### Component Props

#### GoogleSignInButton
- All existing props remain the same
- FedCM is automatically enabled

#### GoogleOneTapFedCM
- `onSuccess: (credential: string) => void` - Success callback
- `onError?: (error: any) => void` - Error callback (optional)
- `disabled?: boolean` - Disable One Tap (optional)

## Error Handling

The implementation includes specific error handling for common FedCM issues:

1. **NotAllowedError**: iframe permission issues
2. **NetworkError**: CSP blocking FedCM
3. **SecurityError**: Cross-origin restrictions

## Migration Benefits

1. **Privacy**: Reduced tracking across sites
2. **User Experience**: Smoother sign-in flow
3. **Future-Proof**: Aligned with web standards
4. **Browser Integration**: Better integration with browser UI

## Troubleshooting

### Common Issues

1. **FedCM not working in iframe**
   - Ensure parent frame has `allow="identity-credentials-get"`

2. **CSP blocking FedCM**
   - Update Content Security Policy to allow `https://accounts.google.com`

3. **Cross-origin issues**
   - Ensure proper CORS configuration
   - Use same-site cross-origin iframes only

### Debug Mode

Set `REACT_APP_SKIP_ONE_TAP=true` to disable One Tap and test only the button flow.

## References

- [Google FedCM Documentation](https://developers.google.com/identity/sign-in/web/gsi-with-fedcm)
- [FedCM Web Standard](https://fedidcg.github.io/FedCM/)
- [Browser Support](https://caniuse.com/fedcm)
